package com.tyt.cargo.invoice.web.service.car.impl;

import com.alibaba.excel.EasyExcel;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.exception.TytException;
import com.tyt.cargo.core.model.ResponseCode;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.core.util.StringBaseUtils;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.db.model.PageParameter;
import com.tyt.cargo.db.tool.CustomPageHelper;
import com.tyt.cargo.invoice.client.enums.DriverTagEnum;
import com.tyt.cargo.invoice.client.vo.base.ExcelImportResultVo;
import com.tyt.cargo.invoice.client.vo.car.*;
import com.tyt.cargo.invoice.client.vo.excel.ImportBatchDelVo;
import com.tyt.cargo.invoice.client.vo.excel.ImportSigningVo;
import com.tyt.cargo.invoice.web.listener.activity.ImportActivityUserDelExcelListener;
import com.tyt.cargo.invoice.web.listener.car.ImportSigningExcelListener;
import com.tyt.cargo.invoice.web.mybatis.entity.base.*;
import com.tyt.cargo.invoice.web.mybatis.mapper.base.*;
import com.tyt.cargo.invoice.web.resp.CarForSigningInfo;
import com.tyt.cargo.invoice.web.resp.SigningCarSaveInfo;
import com.tyt.cargo.invoice.web.service.activity.MarketingActivityUserService;
import com.tyt.cargo.invoice.web.service.base.TytSourceService;
import com.tyt.cargo.invoice.web.service.car.DispatchCompanyService;
import com.tyt.cargo.invoice.web.service.car.TytSigningCarService;
import com.tyt.cargo.invoice.web.service.dispatch.owner.DispatchCooperativeService;
import com.tyt.cargo.invoice.web.service.job.BatchJobService;
import jodd.util.CollectionUtil;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.BeanUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.scheduling.annotation.Async;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import javax.servlet.http.HttpServletResponse;
import java.net.URLEncoder;
import java.util.stream.Collectors;
import com.alibaba.excel.ExcelWriter;
import com.alibaba.excel.write.metadata.WriteSheet;
import com.tyt.cargo.invoice.web.utils.TimeUtil;

import java.io.File;
import java.io.FileInputStream;
import java.io.InputStream;
import java.math.BigDecimal;
import java.util.*;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/03/25 15:53
 */
@Service
@Slf4j
public class TytSigningCarServiceImpl implements TytSigningCarService {

    @Autowired
    private TytSigningCarMapper tytSigningCarMapper;

    @Autowired
    private TytSigningCarInfoMapper tytSigningCarInfoMapper;

    @Autowired
    private BatchJobService batchJobService;

    @Autowired
    private TytUserMapper tytUserMapper;

    private static final String SIGNING_COOPERATION_AGREEMENT = "signing_cooperation_agreement";

    @Autowired
    private TytSourceService tytSourceService;

    @Autowired
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;

    @Autowired
    private TytSigningDriverBlackMapper tytSigningDriverBlackMapper;

    @Autowired
    private TytSigningCarInfoRouteMapper tytSigningCarInfoRouteMapper;

    @Autowired
    private TytCarMapper tytCarMapper;

    @Autowired
    private TytSigningCarInfoCityMapper tytSigningCarInfoCityMapper;

    @Autowired
    private DispatchCompanyService dispatchCompanyService;

    @Autowired
    private DispatchCooperativeService dispatchCooperativeService;

    @Autowired
    private TytCityMapper tytCityMapper;

    private static final String[] drivingAbility = {"挖掘机", "装载机", "自卸车", "高空作业车", "收割机", "压路机", "起重机", "推土机", "打桩机", "旋挖钻机", "摊铺机", "平地机", "铣刨机"};

    private static final String[] DISTANCE_PREFERENCE = {"长途", "短途", "长途/短途"};


    @Override
    public PageData<AssignCarListBean> list(AssignCarListReq req) {
        CustomPageHelper pageHelper = CustomPageHelper.startPage(new PageParameter(req.getPageNum(), req.getPageSize()));
        List<AssignCarListBean> list = tytSigningCarMapper.selectList(req);
        if (CollectionUtils.isNotEmpty(list)) {
            for (AssignCarListBean car : list) {
                Long id = tytSigningCarMapper.getDateOrder(car.getUserId());
                if (null != id) {
                    car.setOrders(true);
                }
                //根据司机手机查询拉黑表
                TytSigningDriverBlack black = tytSigningDriverBlackMapper.getByCellPhone(car.getDriverPhone());
                car.setBlackStatus(1);
                if (Objects.nonNull(black) && black.getSignStatus() == 2) {
                    car.setBlackStatus(2);
                }
                //获取路线
                List<RouteBean> routeList = tytSigningCarInfoRouteMapper.getRouteList(car.getInfoId());
                if (CollectionUtils.isNotEmpty(routeList)) {
                    car.setRouteList(routeList);
                }
            }
        }
        return pageHelper.endPage(list);
    }

    @Override
    public void deleteCar(Long id, Long infoId) {
        Integer count = tytSigningCarInfoMapper.getBySigningId(id);
        //判断当前账号如果有多条车辆，只需删除当前车辆
        if (count == 1) {
            //删除主账号数据
            tytSigningCarMapper.deleteByPrimaryKey(id);
        }
        tytSigningCarInfoMapper.deleteByPrimaryKey(infoId);
    }

    @Override
    public AssignCarListBean carInfo(Long id, Long infoId) {
        TytSigningCar car = tytSigningCarMapper.selectByPrimaryKey(id);
        TytSigningCarInfo info = tytSigningCarInfoMapper.selectByPrimaryKey(infoId);
        AssignCarListBean bean = new AssignCarListBean();
        BeanUtils.copyProperties(car, bean, "updateTime");
        BeanUtils.copyProperties(info, bean, "id");
        //获取路线
        List<RouteBean> routeList = tytSigningCarInfoRouteMapper.getRouteList(infoId);
        if (CollectionUtils.isNotEmpty(routeList)) {
            bean.setRouteList(routeList);
        }
        return bean;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void save(AssignCarListBean bean, TytInternalEmployee sessionEmployee) {
        SigningCarSaveInfo saveInfo = checkParam(bean);
        bean.setUserId(saveInfo.getUserId());
        String operator = sessionEmployee.getName();
        if (null == bean.getId()) {
            bean.setManualInspection(1);
            initSigningCar(bean, saveInfo, operator);
            return;
        }
        updateSigningCar(bean, saveInfo, operator);
    }

    private SigningCarSaveInfo checkParam(AssignCarListBean bean) {
        // 判断是账号是否注册
        Long id = tytUserMapper.getByPhone(bean.getTytCellPhone());
        if (null == id) {
            throw TytException.createException(ResponseEnum.request_error.info("特运通账号不存在"));
        }
        bean.setUserId(id);
        //重复性校验
        // 根据特运通账号获取signingCar表数据
        TytSigningCar signingCar = tytSigningCarMapper.getByCellPhone(bean.getTytCellPhone());
        if (Objects.nonNull(signingCar)) {
            Integer count = tytSigningCarInfoMapper.getCountBySigningIdAndDriverInfo(signingCar.getId(), bean.getName(), bean.getHeadCityNo(), bean.getInfoId());
            if (count > 0) {
                throw TytException.createException(ResponseEnum.request_error.info("特运通账号、司机姓名、车头牌照号不能重复"));
            }
        }
        //司机校验
        TytInvoiceDriver invoice = tytInvoiceDriverMapper.getByuserIdAndName(bean.getUserId(), bean.getName());
        if (Objects.isNull(invoice)) {
            throw TytException.createException(ResponseEnum.request_error.info("司机非当前用户司机"));
        }
        if (StringUtils.isNotBlank(bean.getSigning()) && !bean.getSigning().equals("平台") && invoice.getVerifyStatus() !=1){
            throw TytException.createException(ResponseEnum.request_error.info("非'平台'签约合作商，未完成司机认证"));
        }
        //车辆校验
        String headCityNo = bean.getHeadCityNo();
        String tailCityNo = bean.getTailCityNo();
        CarForSigningInfo carInfo = tytCarMapper.getCarInfoForSigningCar(id, headCityNo.substring(0, 1), headCityNo.substring(1), tailCityNo.substring(0, 1), tailCityNo.substring(1));
        if (Objects.isNull(carInfo)) {
            throw TytException.createException(ResponseEnum.request_error.info("车辆非当前用户车辆或未认证通过"));
        }
        SigningCarSaveInfo saveInfo = new SigningCarSaveInfo();
        saveInfo.setUserId(id);
        saveInfo.setSigningCar(signingCar);
        saveInfo.setInvoice(invoice);
        saveInfo.setCarInfo(carInfo);
        return saveInfo;
    }

    private void initSigningCar(AssignCarListBean bean, SigningCarSaveInfo saveInfo, String operator) {
        TytSigningCar signingCar = saveInfo.getSigningCar();
        CarForSigningInfo carInfo = saveInfo.getCarInfo();
        if (StringUtils.isNotBlank(carInfo.getTLength())) {
            BigDecimal length = new BigDecimal(carInfo.getTLength()).movePointLeft(3);
            bean.setLength(length.toString());
        }
        if (carInfo.getCarType() != null) {
            String carType = getSourceName("tail_car_type", carInfo.getCarType().toString());
            if (StringUtils.isNotBlank(carType)) {
                bean.setCarType(carType);
            }
        }
        if (carInfo.getIsPureFlat() != null) {
            String isPureFlat = getSourceName("tail_car_style", carInfo.getIsPureFlat().toString());
            if (StringUtils.isNotBlank(isPureFlat)) {
                bean.setOtherPureFlat(isPureFlat);
            }
        }
        Long id;
        if (Objects.isNull(signingCar)) {
            //保存账号信息
            id = saveSingingCar(bean, operator);
        } else {
            id = signingCar.getId();
            signingCar.setSigning(bean.getSigning());
            signingCar.setUpdateTime(new Date());
            tytSigningCarMapper.updateByPrimaryKey(signingCar);
        }
        TytInvoiceDriver invoice = saveInfo.getInvoice();
        //保存车辆信息
        saveSingingCarInfo(bean, operator, invoice, id);
    }


    private Long saveSingingCar(AssignCarListBean bean, String operator) {
        TytSigningCar car = new TytSigningCar();
        Date now = new Date();
        BeanUtils.copyProperties(bean, car, "id");
        car.setCreateTime(now);
        car.setUpdateTime(now);
        car.setCreateName(operator);
        car.setCooperateNum(0);
        car.setAssignNum(0);
        car.setAssignSuccessNum(0);
        tytSigningCarMapper.insertSelective(car);
        return car.getId();
    }


    private void saveSingingCarInfo(AssignCarListBean bean, String operator, TytInvoiceDriver invoice, Long singingId) {
        TytSigningCarInfo info = new TytSigningCarInfo();
        Date now = new Date();
        BeanUtils.copyProperties(bean, info, "id");
        info.setUpdateTime(now);
        info.setUpdateName(operator);
        info.setDriverId(invoice.getId());
        info.setDriverUserId(invoice.getDriverUserId());
        info.setDriverPhone(invoice.getPhone());
        if (bean.getManualInspection() != null && bean.getManualInspection() == 1) {
            info.setManualInspection(1);
        } else {
            info.setManualInspection(0);
        }
        if (bean.getInfoId() != null) {
            info.setId(bean.getInfoId());
            tytSigningCarInfoMapper.updateByPrimaryKeySelective(info);
        } else {
            info.setSigningId(singingId);
            info.setCreateTime(now);
            info.setCreateName(operator);
            tytSigningCarInfoMapper.insertSelective(info);
        }
        // 删除城市
        tytSigningCarInfoCityMapper.deleteByInfoId(info.getId());
        //删除路线
        tytSigningCarInfoRouteMapper.deleteByInfoId(info.getId());
        saveCityAndRoute(bean.getProvince(), bean.getCity(), null, null, bean.getRouteList(), info.getId());
    }

    private void updateSigningCar(AssignCarListBean bean, SigningCarSaveInfo saveInfo, String operator) {
        TytSigningCar signingCar = saveInfo.getSigningCar();
        Long id = bean.getId();
        //查询修改后的账号与数据库里的账号有匹配
        if (Objects.isNull(signingCar)) {
            id = saveSingingCar(bean, operator);
            updateSigningCarInfo(id, bean, saveInfo.getInvoice(), operator);
            return;
        }
        //账号为之前的账号
        if (Objects.equals(signingCar.getId(), id)) {
            //更新账号信息
            tytSigningCarMapper.update(bean, operator);
            //更新车辆信息
            TytSigningCarInfo info = tytSigningCarInfoMapper.selectByPrimaryKey(bean.getInfoId());
            BeanUtils.copyProperties(bean, info, "id");

        } else {
            //账号不为之前的账号,如果只剩一条记录
            Integer count = tytSigningCarInfoMapper.getBySigningId(bean.getId());
            //判断当前账号如果有多条车辆，只需删除当前车辆
            if (count == 1) {
                //删除主账号数据
                tytSigningCarMapper.deleteByPrimaryKey(bean.getId());
            }
            signingCar.setSigning(bean.getSigning());
            signingCar.setUpdateTime(new Date());
            tytSigningCarMapper.updateByPrimaryKey(signingCar);
            //更新车辆信息
            TytSigningCarInfo info = tytSigningCarInfoMapper.selectByPrimaryKey(bean.getInfoId());
            BeanUtils.copyProperties(bean, info, "id");
            id = signingCar.getId();
        }
        updateSigningCarInfo(id, bean, saveInfo.getInvoice(), operator);
    }

    private void updateSigningCarInfo(Long signingId, AssignCarListBean bean, TytInvoiceDriver invoice, String operator) {
        TytSigningCarInfo info = tytSigningCarInfoMapper.selectByPrimaryKey(bean.getInfoId());
        BeanUtils.copyProperties(bean, info, "id","assignNum","assignSuccessNum","receivingOrders");
        info.setSigningId(signingId);
        info.setUpdateName(operator);
        info.setUpdateTime(new Date());
        info.setDriverId(invoice.getId());
        info.setDriverUserId(invoice.getDriverUserId());
        info.setDriverPhone(invoice.getPhone());
        //审核通过
        if (bean.getManualInspection() != null && bean.getManualInspection() == 1) {
            info.setManualInspection(1);
        }
        tytSigningCarInfoMapper.updateByPrimaryKeySelective(info);
        // 删除城市
        tytSigningCarInfoCityMapper.deleteByInfoId(info.getId());
        //删除路线
        tytSigningCarInfoRouteMapper.deleteByInfoId(info.getId());
        //保存城市路线
        saveCityAndRoute(bean.getProvince(), bean.getCity(), bean.getAutoCity(), bean.getBiCity(), bean.getRouteList(), info.getId());
    }

    private void saveCityAndRoute(String provinceList, String cityList, String autoCityList, String biCityList, List<RouteBean> routeList, Long infoId) {
        Date now = new Date();
        if (StringUtils.isNotBlank(provinceList)){
            String[] provinces = provinceList.replace("，", ",").split(",");
            for (String province : provinces) {
                TytSigningCarInfoCity infoCity = new TytSigningCarInfoCity();
                infoCity.setCarInfoId(infoId);
                infoCity.setCreateTime(now);
                infoCity.setModifyTime(now);
                infoCity.setCity(province);
                infoCity.setType(1);
                infoCity.setAutoType(0);
                tytSigningCarInfoCityMapper.insert(infoCity);
            }
        }
        if (StringUtils.isNotBlank(cityList)) {
            String[] citys = cityList.replace("，", ",").split(",");
            for (String city : citys) {
                TytSigningCarInfoCity infoCity = new TytSigningCarInfoCity();
                infoCity.setCarInfoId(infoId);
                infoCity.setCreateTime(now);
                infoCity.setModifyTime(now);
                infoCity.setCity(city);
                infoCity.setType(2);
                infoCity.setAutoType(0);
                tytSigningCarInfoCityMapper.insert(infoCity);
            }
        }
        if (StringUtils.isNotBlank(autoCityList)) {
            String[] autoCitys = autoCityList.replace("，", ",").split(",");
            for (String autoCity : autoCitys) {
                TytSigningCarInfoCity infoCity = new TytSigningCarInfoCity();
                infoCity.setCarInfoId(infoId);
                infoCity.setCreateTime(now);
                infoCity.setModifyTime(now);
                infoCity.setCity(autoCity);
                infoCity.setType(2);
                infoCity.setAutoType(1);
                tytSigningCarInfoCityMapper.insert(infoCity);
            }
        }
        if (StringUtils.isNotBlank(biCityList)) {
            String[] biCitys = biCityList.replace("，", ",").split(",");
            for (String biCity : biCitys) {
                TytSigningCarInfoCity infoCity = new TytSigningCarInfoCity();
                infoCity.setCarInfoId(infoId);
                infoCity.setCreateTime(now);
                infoCity.setModifyTime(now);
                infoCity.setCity(biCity);
                infoCity.setType(2);
                infoCity.setAutoType(2);
                tytSigningCarInfoCityMapper.insert(infoCity);
            }
        }
        //保存路线
        if (CollectionUtils.isNotEmpty(routeList)) {
            for (RouteBean route : routeList) {
                TytSigningCarInfoRoute infoRoute = new TytSigningCarInfoRoute();
                infoRoute.setCarInfoId(infoId);
                infoRoute.setCreateTime(now);
                infoRoute.setModifyTime(now);
                infoRoute.setStartCity(route.getStartCity());
                infoRoute.setDestCity(route.getDestCity());
                tytSigningCarInfoRouteMapper.insert(infoRoute);
            }
        }
    }


    @Async
    @Override
    public void importUsersDel(TytSigningCarService tytSigningCarService, TytBatchJob job, TytInternalEmployee employee) {
        File jobFileTmp = null;
        try (ImportSigningExcelListener userImportListener = new ImportSigningExcelListener(tytSigningCarService, batchJobService, job, employee)) {

            jobFileTmp = batchJobService.downloadJobFile(job);

            try (InputStream input = new FileInputStream(jobFileTmp)) {
                EasyExcel.read(input, ImportSigningVo.class, userImportListener).sheet().headRowNumber(1).doRead();

                ExcelImportResultVo importResult = userImportListener.getImportResult();

                batchJobService.finishJob(job, importResult);
            }

        } catch (Exception e) {
            TytException tytException = TytException.createException(e);
            batchJobService.failJob(job, tytException.getErrorMsg());
            log.error("Batch_Job_Error : {}", job.getId(), e);
        } finally {
            batchJobService.deleteJobFile(jobFileTmp);
        }
    }

    @Override
    public void saveAssign(String name, ImportSigningVo vo) {
        AssignCarListBean bean = checkData(vo);
        TytSigningCar signingCar = tytSigningCarMapper.getByCellPhone(bean.getTytCellPhone());
        if (signingCar != null && signingCar.getId() != null) {
            Long infoId = tytSigningCarInfoMapper.getBySigningIdAndDriverName(signingCar.getId(), bean.getName());
            if (infoId != null) {
                bean.setInfoId(infoId);
            }
        }
        if (StringUtils.isNotBlank(vo.getDriverTagStr()) && DriverTagEnum.FULL_TIME.getName().equals(vo.getDriverTagStr())) {
            bean.setDriverTag(DriverTagEnum.FULL_TIME.getCode());
        }
        SigningCarSaveInfo saveInfo = checkParam(bean);
        initSigningCar(bean, saveInfo, name);
    }

    @Override
    public TytSigningCarInfo saveSpecialCar(TytSpecialCar car, String realName,List<RouteBean> routes) {
        //新增重复性校验
        AssignCarListBean bean = new AssignCarListBean();
        BeanUtils.copyProperties( car, bean);
        checkParam(bean);

        //查询signCar
        TytSigningCar signingCar = tytSigningCarMapper.getByCellPhone(car.getTytCellPhone());
        Date now = new Date();
        if (Objects.isNull(signingCar)) {
            signingCar = new TytSigningCar();
            BeanUtils.copyProperties(car, signingCar, "id");
            signingCar.setCreateTime(now);
            signingCar.setUpdateTime(now);
            signingCar.setCreateName(realName);
            signingCar.setCooperateNum(0);
            signingCar.setAssignNum(0);
            signingCar.setAssignSuccessNum(0);
            //签约合作商，目前只有一个，直接写死
            signingCar.setSigning("平台");
            signingCar.setBiDistance(car.getBiDistance());
            tytSigningCarMapper.insertSelective(signingCar);
        }
        Long signingCarId = signingCar.getId();
        TytSigningCarInfo info = new TytSigningCarInfo();
        BeanUtils.copyProperties(car, info, "id","assignNum","assignSuccessNum","receivingOrders");
        info.setSigningId(signingCarId);
        info.setDriverPhone(car.getDriverPhone());
        info.setCreateTime(now);
        info.setUpdateTime(now);
        info.setCreateName(realName);
        info.setUpdateName(realName);
        info.setSourceType(2);
        info.setManualInspection(1);
        TytInvoiceDriver invoice = tytInvoiceDriverMapper.getByuserIdAndName(car.getUserId(), info.getName());
        if (null != invoice) {
            info.setDriverId(invoice.getId());
            info.setDriverUserId(invoice.getDriverUserId());
            info.setDriverPhone(invoice.getPhone());
        }
        tytSigningCarInfoMapper.insertSelective(info);
        saveCityAndRoute(null, car.getCity(), null, null, routes,info.getId());
        return info;
    }

    @Override
    public void exportSigningCarList(HttpServletResponse response, AssignCarListReq req) throws Exception {
        //设置相关参数
        response.setContentType("application/vnd.openxmlformats-officedocument.spreadsheetml.sheet");
        response.setCharacterEncoding("utf-8");
        // 这里URLEncoder.encode可以防止中文乱码
        String fileName = URLEncoder.encode("专车车辆_" + new Date().getTime(), "UTF-8");
        response.setHeader("Content-disposition", "attachment;filename="+ fileName + ".xlsx");
        ExcelWriter excelWriter = null;
        try {
            excelWriter = EasyExcel.write(response.getOutputStream(), TytSigningCarExport.class).build();
            WriteSheet writeSheet = EasyExcel.writerSheet("专车车辆导出").build();
            while(true){
                PageData<AssignCarListBean> pageDataDo = list(req);
                if(Objects.isNull(pageDataDo)|| CollectionUtils.isEmpty(pageDataDo.getList())){
                    log.info("exportSigningCarList list currentPage: {} " ,req.getPageNum());
                    return;
                }
                List<AssignCarListBean> carList = pageDataDo.getList();
                List<TytSigningCarExport> carExportList = convertSigningCarExportList(carList);
                excelWriter.write(carExportList, writeSheet);
                req.setPageNum(req.getPageNum()+1);
            }
        } catch (Exception e) {
            log.error("专车车辆导出异常", e);
        } finally {
            if(excelWriter!=null){
                excelWriter.finish();
            }
        }
    }

    @Override
    public List<SigningCarInfoGpscheatDTO> specialCarGPSCheatByCarInfoId(Long carInfoId) {
        return tytSigningCarInfoCityMapper.specialCarGPSCheatByCarInfoId(carInfoId);
    }

    /**
     * 转换专车车辆导出数据
     *
     * @param carList 专车车辆列表
     * @return 导出数据列表
     */
    private List<TytSigningCarExport> convertSigningCarExportList(List<AssignCarListBean> carList) {
        //转换
        return carList.stream()
                .map(car -> {
                    TytSigningCarExport export = new TytSigningCarExport();
                    export.setUserId(car.getUserId() == null ? "" : car.getUserId().toString());
                    export.setName(car.getName() == null ? "" : car.getName());
                    export.setProvince(car.getProvince() == null ? "" : car.getProvince());
                    export.setCity(car.getCity() == null ? "" : car.getCity());
                    export.setAutoCity(car.getAutoCity() == null ? "" : car.getAutoCity());
                    export.setBiCity(car.getBiCity() == null ? "" : car.getBiCity());
                    export.setSigning(car.getSigning() == null ? "" : car.getSigning());
                    export.setCarType(car.getCarType() == null ? "" : car.getCarType());
                    export.setCarNo((car.getHeadCityNo() == null ? "" : car.getHeadCityNo()) + " " + (car.getTailCityNo() == null ? "" : car.getTailCityNo()));
                    export.setLength(car.getLength() == null ? "" : car.getLength());
                    export.setTableHeight(car.getTableHeight() == null ? "" : car.getTableHeight());
                    export.setOtherPureFlat(car.getOtherPureFlat() == null ? "" : car.getOtherPureFlat());
                    export.setLadderType(car.getLadderType() == null ? "" : car.getLadderType());
                    export.setRemark(car.getRemark() == null ? "" : car.getRemark());

                    // 司机标签处理
                    if (car.getDriverTag() != null) {
                        if (car.getDriverTag().equals(DriverTagEnum.FULL_TIME.getCode())) {
                            export.setDriverTagString(DriverTagEnum.FULL_TIME.getName());
                        } else if (car.getDriverTag().equals(DriverTagEnum.PART_TIME.getCode())) {
                            export.setDriverTagString(DriverTagEnum.PART_TIME.getName());
                        } else {
                            export.setDriverTagString("");
                        }
                    } else {
                        export.setDriverTagString("");
                    }

                    export.setDistancePreference(car.getDistancePreference() == null ? "" : car.getDistancePreference());
                    export.setCreateTime(car.getCreateTime() == null ? "" : TimeUtil.formatDateTime(car.getCreateTime()));
                    export.setUpdateTime(car.getUpdateTime() == null ? "" : TimeUtil.formatDateTime(car.getUpdateTime()));

                    export.setDispatch(car.getDispatch() == null ? "" : car.getDispatch());

                    if (CollectionUtils.isNotEmpty(car.getRouteList())) {
                        export.setRouteString(car.getRouteList().stream().map(route -> route.getStartCity() + "-" + route.getDestCity()).collect(Collectors.joining(",")));
                    }

                    export.setDrivingAbility(car.getDrivingAbility() == null ? "" : car.getDrivingAbility());
                    export.setOrdersString(car.getOrders() == null ? "" : car.getOrders() ? "是" : "否");
                    export.setStatusString(car.getStatus() == null ? "" : car.getStatus() == 1 ? "是" : "否");
                    export.setReceivingOrders(car.getReceivingOrders() == null ? "" : car.getReceivingOrders());
                    export.setFavorableComment(car.getFavorableComment() == null ? "" : car.getFavorableComment());
                    export.setCooperateNum(car.getCooperateNum() == null ? "" : car.getCooperateNum().toString());
                    export.setAssignNum(car.getAssignNum() == null ? "" : car.getAssignNum().toString());
                    export.setAssignSuccessNum(car.getAssignSuccessNum() == null ? "" : car.getAssignSuccessNum().toString());

                    export.setCounty(car.getCounty() == null ? "" : car.getCounty());

                    export.setSourceTypeString(car.getSourceType() == null ? "" : car.getSourceType() == 1 ? "人工加入" : car.getSourceType() == 2 ? "申请加入" : "成交加入");

                    export.setManualInspectionString(car.getManualInspection() == null ? "" : car.getManualInspection() == 1 ? "已核实" : "未核实");

                    return export;
                }).collect(Collectors.toList());
    }

    private String getSourceName(String groupCode, String value) {
        List<TytSource> sources = tytSourceService.getGroupSourceList(groupCode);
        if (CollectionUtils.isEmpty(sources)) {
            return null;
        }
        for (TytSource source : sources) {
            if (source.getValue().equals(value)) {
                return source.getName();
            }
        }
        return null;
    }

    private AssignCarListBean checkData(ImportSigningVo vo) {

        AssignCarListBean bean = new AssignCarListBean();
        BeanUtils.copyProperties(vo, bean);

        //运输距离偏好校验
        if (StringUtils.isNotBlank(vo.getDistancePreference())){
            if (!Arrays.asList(DISTANCE_PREFERENCE).contains(vo.getDistancePreference())){
                throw TytException.createException(ResponseEnum.request_error.info("运输距离偏好不正确"));
            }
        }

        int cityCount = 0;
        //验证城市是否有重复
        if (StringUtils.isNotBlank(bean.getCity())) {
            String cityStr = bean.getCity().replace("，", ",").trim();
            String[] cityList = cityStr.split(",");
            if (cityList.length > 5) {
                throw TytException.createException(ResponseEnum.request_error.info("城市最多只能有5个"));
            }
            Set<String> citySet = new HashSet<>();
            for (String city : cityList) {
                if (!(city.endsWith("市") || city.endsWith("区") || city.endsWith("县") || city.endsWith("州") || city.endsWith("盟")) && !"全国".equals(city)) {
                    throw TytException.createException(ResponseEnum.request_error.info("城市需有市字，例如：北京市"));
                }
                if (citySet.contains(city)) {
                    throw TytException.createException(ResponseEnum.request_error.info("城市不能重复"));
                }
                citySet.add(city);
            }
            cityCount = citySet.size();
            citySet.clear();
            bean.setCity(cityStr);
        }
        //省份
        if (StringUtils.isNotBlank(bean.getProvince())) {
            String provinceStr = bean.getProvince().replace("，", ",").trim();
            String[] provinceList = provinceStr.split(",");
            if (provinceList.length > 5) {
                throw TytException.createException(ResponseEnum.request_error.info("省份最多只能有5个"));
            }
            Set<String> provinceSet = new HashSet<>();
            for (String province : provinceList) {
                if (provinceSet.contains(province)) {
                    throw TytException.createException(ResponseEnum.request_error.info("省份不能重复"));
                }
                String provinceName = tytCityMapper.checkProvinceExist(province);
                if (StringUtils.isBlank(provinceName)){
                    throw TytException.createException(ResponseEnum.request_error.info("省份输入框请去掉省字"));
                }
                provinceSet.add(province);
            }
            cityCount = cityCount + provinceSet.size();
            provinceSet.clear();
            bean.setProvince(provinceStr);
        }
        if (cityCount>5){
            throw TytException.createException(ResponseEnum.request_error.info("省份+城市最多只能有5个"));
        }

        //签约合作商校验
        if (StringUtils.isNotBlank(bean.getSigning())) {
            String signingStr = bean.getSigning().replace("，", ",").trim();
            String[] signingList = signingStr.split(",");
            Set<String> signingSet = new HashSet<>();
            for (String signing : signingList) {
                if (signingSet.contains(signing)) {
                    throw TytException.createException(ResponseEnum.request_error.info("签约合作商不能重复"));
                }
                TytDispatchCooperative cooperative = dispatchCooperativeService.getValidByCooperativeName(signing);
                if (Objects.isNull(cooperative)){
                    throw TytException.createException(ResponseEnum.request_error.info("签约合作商不正确"));
                }
                signingSet.add(signing);
            }
            bean.setSigning(signingStr);
        } else {
            bean.setSigning("平台");
        }
        //驾驶能力校验
        if (StringUtils.isNotBlank(bean.getDrivingAbility())) {
            if (bean.getDrivingAbility().equals("无")) {
                bean.setDrivingAbility(null);
            } else {
                String abilityStr = bean.getDrivingAbility().replace("，", ",").trim();
                String[] abilityList = abilityStr.split(",");
                Set<String> abilitySet = new HashSet<>();
                for (String ability : abilityList) {
                    if (abilitySet.contains(ability)) {
                        throw TytException.createException(ResponseEnum.request_error.info("驾驶能力不能重复"));
                    }
                    boolean contains = Arrays.asList(drivingAbility).contains(ability);
                    if (!contains) {
                        throw TytException.createException(ResponseEnum.request_error.info("驾驶能力不正确"));
                    }
                    abilitySet.add(ability);
                }
                abilitySet.clear();
                bean.setDrivingAbility(abilityStr);
            }
        }
        //路线校验
        List<RouteBean> routeList = new ArrayList<>();
        if (StringUtils.isNotBlank(vo.getRout())) {
            String[] rout = vo.getRout().replace("，", ",").trim().split(",");
            if (rout.length > 5){
                throw TytException.createException(ResponseEnum.request_error.info("路线最多只能有5个"));
            }
            Set<String> citySet = new HashSet<>();
            for (String s : rout) {
                if (StringUtils.isNotBlank(s)) {
                    String[] point = s.split("-");
                    if (!(point[0].endsWith("市") || point[0].endsWith("区") || point[0].endsWith("县") || point[0].endsWith("州") || point[0].endsWith("盟"))) {
                        throw TytException.createException(ResponseEnum.request_error.info("路线城市需有市字，例如：北京市-上海市"));
                    }
                    if (!(point[1].endsWith("市") || point[1].endsWith("区") || point[1].endsWith("县") || point[1].endsWith("州") || point[1].endsWith("盟"))) {
                        throw TytException.createException(ResponseEnum.request_error.info("路线城市需有市字，例如：北京市-上海市"));
                    }
                    if (citySet.contains(s)) {
                        throw TytException.createException(ResponseEnum.request_error.info("路线不能重复"));
                    }
                    citySet.add(s);
                    RouteBean routeBean = RouteBean.builder().startCity(point[0]).destCity(point[1]).build();
                    routeList.add(routeBean);
                }
            }
            bean.setRouteList(routeList);
            citySet.clear();
        }

        //调度校验
        if (StringUtils.isNotBlank(bean.getDispatch())) {
            Integer count = dispatchCompanyService.getCountByName(bean.getDispatch());
            if (count <= 0) {
                throw TytException.createException(ResponseEnum.request_error.info("调度不正确"));
            }
        }
        return bean;
    }
}
