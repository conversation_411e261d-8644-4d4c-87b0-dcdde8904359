package com.tyt.cargo.invoice.web.mybatis.entity.base;

import java.math.BigDecimal;
import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_signing_car_info")
public class TytSigningCarInfo {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * 签约车辆id
     */
    @Column(name = "signing_id")
    private Long signingId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 司机表id
     */
    @Column(name = "driver_id")
    private Long driverId;

    /**
     * 司机用户id
     */
    @Column(name = "driver_user_id")
    private Long driverUserId;

    /**
     * 司机手机号
     */
    @Column(name = "driver_phone")
    private String driverPhone;

    /**
     * 好评率
     */
    @Column(name = "favorable_comment")
    private BigDecimal favorableComment;

    /**
     * 综合分值
     */
    @Column(name = "compre_fraction")
    private BigDecimal compreFraction;

    /**
     * 联系电话
     */
    private String phone;

    /**
     * 市区
     */
    private String city;

    /**
     * 自动市区
     */
    @Column(name = "auto_city")
    private String autoCity;

    /**
     * 经分市区
     */
    @Column(name = "bi_city")
    private String biCity;

    /**
     * 归属调度
     */
    private String dispatch;

    /**
     * 仓库
     */
    private String county;

    /**
     * 车型
     */
    @Column(name = "car_type")
    private String carType;

    /**
     * 车头信息
     */
    @Column(name = "head_city_no")
    private String headCityNo;

    /**
     * 挂车信息
     */
    @Column(name = "tail_city_no")
    private String tailCityNo;

    /**
     * 挂车长度(米)
     */
    private String length;

    /**
     * 货台面高度（厘米）
     */
    @Column(name = "table_height")
    private String tableHeight;

    /**
     * 平台样式
     */
    @Column(name = "other_pure_flat")
    private String otherPureFlat;

    /**
     * 爬梯类型
     */
    @Column(name = "ladder_type")
    private String ladderType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "update_time")
    private Date updateTime;

    /**
     * 创建人
     */
    @Column(name = "create_name")
    private String createName;

    /**
     * 更新人
     */
    @Column(name = "update_name")
    private String updateName;

    /**
     * 驾驶能力 逗号分隔
     */
    @Column(name = "driving_ability")
    private String drivingAbility;

    /**
     * 指派次数
     */
    @Column(name = "assign_num")
    private Integer assignNum;

    /**
     * 指派成功次数
     */
    @Column(name = "assign_success_num")
    private Integer assignSuccessNum;

    /**
     * 接单率
     */
    @Column(name = "receiving_orders")
    private BigDecimal receivingOrders;

    /**
     * 距离偏好
     */
    @Column(name = "distance_preference")
    private String distancePreference;

    /**
     * 省
     */
    private String province;

    /**
     * 加入方式 1：人工加入；2：申请加入；3：成交加入
     */
    @Column(name = "source_type")
    private Integer sourceType;

    /**
     * 人工核实 0：未核实；1：已核实
     */
    @Column(name = "manual_inspection")
    private Integer manualInspection;

    /**
     * 司机标签：1-兼职运力，2-全职运力
     */
    @Column(name = "driver_tag")
    private Integer driverTag;
}