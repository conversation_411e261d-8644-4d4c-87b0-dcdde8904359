package com.tyt.cargo.invoice.web.controller.card;

import cn.hutool.core.util.ObjectUtil;
import com.tyt.cargo.core.enums.ResponseEnum;
import com.tyt.cargo.core.model.ResponseCode;
import com.tyt.cargo.core.model.WebResult;
import com.tyt.cargo.db.model.PageData;
import com.tyt.cargo.invoice.client.enums.BatchJobTypeEnum;
import com.tyt.cargo.invoice.client.vo.car.AssignCarListBean;
import com.tyt.cargo.invoice.client.vo.car.AssignCarListReq;
import com.tyt.cargo.invoice.client.vo.car.SigningCarInfoGpscheatDTO;
import com.tyt.cargo.invoice.web.mybatis.entity.base.TytBatchJob;
import com.tyt.cargo.invoice.web.mybatis.entity.base.TytDispatchCompany;
import com.tyt.cargo.invoice.web.mybatis.entity.base.TytInternalEmployee;
import com.tyt.cargo.invoice.web.mybatis.mapper.base.TytInvoiceDriverMapper;
import com.tyt.cargo.invoice.web.mybatis.mapper.base.TytUserMapper;
import com.tyt.cargo.invoice.web.service.base.ManagerBaseService;
import com.tyt.cargo.invoice.web.service.car.DispatchCompanyService;
import com.tyt.cargo.invoice.web.service.car.TytSigningCarService;
import com.tyt.cargo.invoice.web.service.job.BatchJobService;
import lombok.extern.slf4j.Slf4j;
import org.apache.commons.collections4.CollectionUtils;
import org.apache.commons.lang3.StringUtils;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.beans.factory.annotation.Qualifier;
import org.springframework.web.bind.annotation.*;

import javax.servlet.http.HttpServletResponse;
import java.util.ArrayList;
import java.util.Calendar;
import java.util.Date;
import java.util.List;
import java.util.Objects;

/**
 * <AUTHOR>
 * @version 1.0
 * @description
 * @date 2024/03/25 15:54
 */
@RestController
@RequestMapping("/assign/car")
@Slf4j
public class TytSigningCarController {


    @Autowired
    private TytSigningCarService tytSigningCarService;

    @Qualifier("managerBaseServiceImpl")
    @Autowired
    private ManagerBaseService managerBaseService;

    @Autowired
    private TytUserMapper tytUserMapper;

    @Autowired
    private BatchJobService batchJobService;

    @Autowired
    private TytInvoiceDriverMapper tytInvoiceDriverMapper;

    @Autowired
    private DispatchCompanyService dispatchCompanyService;


    /**
     * 签约车辆列表
     *
     * @param req 参数
     * @return webResult
     */
    @PostMapping("/list")
    public WebResult<PageData<AssignCarListBean>> getList(@RequestBody AssignCarListReq req) {
        if (req == null) {
            return WebResult.failResponse(ResponseEnum.request_error.info());
        }
        return WebResult.successResponse(tytSigningCarService.list(req));
    }

    /**
     * 删除签约车辆
     *
     * @param id     主表id
     * @param infoId 详情表id
     * @return WebResult
     */
    @GetMapping("/deleteCar")
    public WebResult getActivityList(@RequestParam Long id, @RequestParam Long infoId) {
        tytSigningCarService.deleteCar(id, infoId);
        return WebResult.successResponse();
    }

    /**
     * 签约车辆详情
     *
     * @param id id
     * @return WebResult<TytSigningCar>
     */
    @GetMapping("/carInfo")
    public WebResult<AssignCarListBean> carInfo(@RequestParam Long id, @RequestParam Long infoId) {
        return WebResult.successResponse(tytSigningCarService.carInfo(id, infoId));
    }

    @RequestMapping("/specialCarGPSCheatByCarInfoId")
    public WebResult<List<SigningCarInfoGpscheatDTO>> specialCarGPSCheatByCarInfoId(@RequestParam("carInfoId") Long carInfoId) {
        return WebResult.successResponse(tytSigningCarService.specialCarGPSCheatByCarInfoId(carInfoId));
    }


    /**
     * 保存签约车辆
     *
     * @param car 参数
     * @return WebResult
     */
    @PostMapping("/save")
    public WebResult carInfo(@RequestBody AssignCarListBean car) {
        //用户是否登录
        TytInternalEmployee sessionEmployee = managerBaseService.getSessionEmployee();
        if (ObjectUtil.isNull(sessionEmployee)) {
            return WebResult.failResponse(ResponseEnum.no_login.info());
        }
        String voCheck = getVOCheck(car);
        if (StringUtils.isNotEmpty(voCheck)) {
            return WebResult.failResponse(new ResponseCode("request_error", voCheck));
        }
        tytSigningCarService.save(car, sessionEmployee);
        return WebResult.successResponse();
    }

    /**
     * 导入
     *
     * @param fileUrl 文件地址
     * @param jobName 任务名称
     * @return WebResult<TytBatchJob>
     */
    @PostMapping("/import")
    public WebResult<TytBatchJob> userBatchDel(@RequestParam String fileUrl, @RequestParam String jobName) {
        TytInternalEmployee employee = managerBaseService.getSessionEmployee();
        // 创建导入任务
        TytBatchJob job = batchJobService.saveBatchJob(employee, jobName, BatchJobTypeEnum.IMPORT_SIGNING_CAR, fileUrl);
        tytSigningCarService.importUsersDel(tytSigningCarService, job, employee);
        return WebResult.successResponse(job);
    }


    /**
     * 空数据校验
     *
     * @param vo 参数
     * @return String
     */
    public String getVOCheck(AssignCarListBean vo) {
        if (StringUtils.isEmpty(vo.getName())) {
            return "姓名为空";
        }
        if (StringUtils.isEmpty(vo.getTytCellPhone())) {
            return "特运通账号为空";
        }
        if (StringUtils.isEmpty(vo.getPhone())) {
            return "联系电话为空";
        }
        if (StringUtils.isEmpty(vo.getCity()) && StringUtils.isBlank(vo.getProvince()) && StringUtils.isBlank(vo.getAutoCity()) && StringUtils.isBlank(vo.getBiCity())) {
            return "地区为空";
        }
        if (StringUtils.isEmpty(vo.getHeadCityNo())) {
            return "车头牌号为空";
        }
        if (StringUtils.isEmpty(vo.getTailCityNo())) {
            return "挂车牌号为空";
        }
        if (StringUtils.isEmpty(vo.getDispatch())) {
            return "调度为空";
        }
        if (Objects.nonNull(vo.getId())){
            if (StringUtils.isEmpty(vo.getCarType())) {
                return "车型为空";
            }
            if (StringUtils.isEmpty(vo.getLength())) {
                return "挂车长度为空";
            }
            if (StringUtils.isEmpty(vo.getOtherPureFlat())) {
                return "平台样式为空";
            }
        }
        if (StringUtils.isBlank(vo.getDistancePreference())){
            return "运输距离偏好不能为空";
        }
        return "";
    }



    @GetMapping("/getPhoneInvoice")
    public WebResult getPhoneInvoice(@RequestParam String phone) {
        if (StringUtils.isEmpty(phone)) {
            return WebResult.failResponse(ResponseEnum.request_error.info());
        }
        Long id = tytUserMapper.getByPhone(phone);
        if (null == id) {
            return WebResult.failResponse(new ResponseCode("10001", "司机账号不存在,请确认后再添加"));
        }
        return WebResult.successResponse(tytInvoiceDriverMapper.getByUserId(id));
    }

    /**
     * 调度人员模糊检索
     * @param name name
     * @return result
     */
    @GetMapping("/getDispatchNames")
    public WebResult<List<TytDispatchCompany>> getDispatchNames(@RequestParam String name){
        if (StringUtils.isBlank(name)){
            return WebResult.successResponse(new ArrayList<>());
        }
        return WebResult.successResponse(dispatchCompanyService.getDispatchNames(name));
    }


    /**
     * 专车车辆列表导出
     *
     * @param req 请求参数
     * @param response HTTP响应
     */
    @PostMapping("/export")
    public void exportExcel(@RequestBody(required = false) AssignCarListReq req, HttpServletResponse response) {
        try {
            //设置导出筛选后的所有页数据
            if (req == null) {
                req = new AssignCarListReq();
            }
            req.setPageNum(1);
            req.setPageSize(200);
            tytSigningCarService.exportSigningCarList(response, req);
        } catch (Exception e) {
            log.error("TytSigningCarController export error", e);
        }
    }
}
