package com.tyt.cargo.invoice.web.mybatis.entity.base;

import java.util.Date;
import javax.persistence.*;
import lombok.AllArgsConstructor;
import lombok.Builder;
import lombok.Data;
import lombok.NoArgsConstructor;

@Data
@Builder
@AllArgsConstructor
@NoArgsConstructor
@Table(name = "tyt_signing_car_info_city")
public class TytSigningCarInfoCity {
    @Id
    @GeneratedValue(strategy = GenerationType.IDENTITY)
    private Long id;

    /**
     * tyt_signing_car_info表的id
     */
    @Column(name = "car_info_id")
    private Long carInfoId;

    /**
     * 类型 1省 2市
     */
    @Column(name = "type")
    private Integer type;

    /**
     * 自动标签 0：无标签 1：自动标签 2：经分标签
     */
    @Column(name = "auto_type")
    private Integer autoType;


    /**
     * 城市
     */
    private String city;

    /**
     * 创建时间
     */
    @Column(name = "create_time")
    private Date createTime;

    /**
     * 更新时间
     */
    @Column(name = "modify_time")
    private Date modifyTime;
}