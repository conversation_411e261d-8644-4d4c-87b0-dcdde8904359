<?xml version="1.0" encoding="UTF-8"?>
<!DOCTYPE mapper PUBLIC "-//mybatis.org//DTD Mapper 3.0//EN" "http://mybatis.org/dtd/mybatis-3-mapper.dtd">
<mapper namespace="com.tyt.cargo.invoice.web.mybatis.mapper.base.TytSigningCarMapper">
  <resultMap id="BaseResultMap" type="com.tyt.cargo.invoice.web.mybatis.entity.base.TytSigningCar">
    <!--
      WARNING - @mbg.generated
    -->
    <id column="id" jdbcType="BIGINT" property="id" />

    <result column="user_id" jdbcType="BIGINT" property="userId" />
    <result column="tyt_cell_phone" jdbcType="VARCHAR" property="tytCellPhone" />

    <result column="signing" jdbcType="VARCHAR" property="signing" />
    <result column="cooperate_num" jdbcType="INTEGER" property="cooperateNum" />
    <result column="assign_num" jdbcType="INTEGER" property="assignNum" />
    <result column="assign_success_num" jdbcType="INTEGER" property="assignSuccessNum" />
    <result column="create_time" jdbcType="TIMESTAMP" property="createTime" />
    <result column="create_name" jdbcType="VARCHAR" property="createName" />
    <result column="update_time" jdbcType="TIMESTAMP" property="updateTime" />
    <result column="update_name" jdbcType="VARCHAR" property="updateName" />
    <result column="bi_distance" jdbcType="VARCHAR" property="biDistance"/>



    <result column="receiving_orders" jdbcType="DECIMAL" property="receivingOrders" />
    <result column="favorable_comment" jdbcType="DECIMAL" property="favorableComment" />
    <result column="compre_fraction" jdbcType="DECIMAL" property="compreFraction" />
    <result column="status" jdbcType="INTEGER" property="status" />

  </resultMap>

  <select id="selectList" resultType="com.tyt.cargo.invoice.client.vo.car.AssignCarListBean">
    select car.id as id, info.name, car.tyt_cell_phone as tytCellPhone, info.phone,car.cooperate_num as cooperateNum,
    info.assign_num as assignNum,info.assign_success_num as assignSuccessNum,info.id as infoId ,
    info.city, info.auto_city as autoCity,info.bi_city as biCity,info.car_type as carType,info.head_city_no as headCityNo, car.signing signing,
    info.tail_city_no as tailCityNo, info.length,info.table_height as tableHeight, info.other_pure_flat as
    otherPureFlat, info.ladder_type as ladderType,info.remark,car.user_id as userId,info.receiving_orders as receivingOrders,
    info.driver_phone driverPhone, info.distance_preference distancePreference,info.province,
    info.favorable_comment as favorableComment,car.status,info.dispatch,info.county,info.driver_user_id as driverUserId, info.driving_ability drivingAbility
    , info.source_type as sourceType, info.manual_inspection as manualInspection, info.update_time as updateTime, info.create_time as createTime,
    info.driver_tag as driverTag, car.car_user_flag as carUserFlag
    from tyt_signing_car car
    left join tyt_signing_car_info info on car.id = info.signing_id
    <where>
      1 = 1
      <if test="req.name != null and req.name != ''">
        and info.name = #{req.name}
      </if>
      <if test="req.tytCellPhone != null and req.tytCellPhone != ''">
        and car.tyt_cell_phone = #{req.tytCellPhone}
      </if>
      <if test="req.phone != null and req.phone != ''">
        and info.phone = #{req.phone}
      </if>
      <if test="req.carUserFlag != null and req.carUserFlag != ''">
        and car.car_user_flag = #{req.carUserFlag}
      </if>
      <if test="req.city != null and req.city != ''">
        and (info.city like CONCAT('%', #{req.city},'%') or info.auto_city like CONCAT('%', #{req.city},'%') or info.bi_city like CONCAT('%', #{req.city},'%'))
      </if>
      <if test="req.county != null and req.county != ''">
        and info.county like CONCAT('%', #{req.county},'%')
      </if>
      <if test="req.dispatch != null and req.dispatch != ''">
        and info.dispatch like CONCAT('%', #{req.dispatch},'%')
      </if>
      <if test="req.province != null and req.province != ''">
        and info.province like CONCAT('%', #{req.province},'%')
      </if>
      <if test="req.signing != null and req.signing != ''">
        and car.signing like CONCAT('%', #{req.signing},'%')
      </if>
      <if test="req.sourceType != null">
        and info.source_type = #{req.sourceType}
      </if>
      <if test="req.manualInspection != null">
        and info.manual_inspection = #{req.manualInspection}
      </if>
      <if test="req.driverTag != null">
        and info.driver_tag = #{req.driverTag}
      </if>
      <if test="req.distancePreference != null and req.distancePreference != ''">
        and info.distance_preference = #{req.distancePreference}
      </if>
      <if test="req.updateStartTime != null">
        AND info.create_time &gt;= #{req.updateStartTime}
      </if>
      <if test="req.updateEndTime != null">
        AND info.create_time &lt;= #{req.updateEndTime}
      </if>
      <if test="req.carType != null and req.carType != ''">
        AND info.car_type like CONCAT('%', #{req.carType},'%')
      </if>
      <choose>
        <when test="req.type != null and req.type != ''">
          <if test="req.type == 1">
            order by car.cooperate_num desc,info.id desc
          </if>
          <if test="req.type == 2">
            order by info.receiving_orders desc,info.id desc
          </if>
          <if test="req.type == 3">
            order by info.favorable_comment desc,info.id desc
          </if>
          <if test="req.type == 4">
            order by info.compre_fraction desc,info.id desc
          </if>
        </when>
        <otherwise>
          order by info.id desc
        </otherwise>
      </choose>
    </where>
  </select>

  <select id="getByUserId" resultType="java.lang.Long">
    select id from tyt_signing_car where user_id = #{id} limit 1
  </select>

  <select id="getDateOrder" resultType="java.lang.Long">
    select id from tyt_transport_orders where ctime >= '2024-3-27' and cost_status  = 15 and pay_user_id = #{userId} limit 1
  </select>

  <update id="update">
    update tyt_signing_car set signing = #{bean.signing},update_time = now()
    ,update_name = #{name} where id = #{bean.id}
  </update>

  <select id="getByCellPhone" resultMap="BaseResultMap">
    select * from tyt_signing_car where tyt_cell_phone = #{tytCellPhone} limit 1
  </select>
</mapper>