package com.tyt.model;

import com.alibaba.fastjson.annotation.JSONField;
import lombok.Data;

import java.io.Serializable;
import java.util.Date;

/**
 * 广告位提供给app，redis反序列化使用
 * 需要兼容manage_new和plat
 *
 * <AUTHOR>
 */

@Data
public class AdPosition implements Serializable {

    private Long id;
    /**
     * 标题
     */
    private String title;
    /**
     * 图片显示位置，参见tyt_source表ad_position
     */
    private Integer showPosition;
    /**
     * 图片url
     */
    private String picUrl;
    /**
     * 图片url
     */
    private String originalFilename;
    /**
     * 缩略图url
     */
    private String smallPicUrl;
    /**
     * 图片链接地址
     */
    private String picLinkUrl;

    /**
     * 文字内容标题
     */
    private String wordContentTitle;
    /**
     * 文字内容
     */
    private String wordContent;
    /**
     * 文字链接
     */
    private String wordLinkUrl;
    /**
     * 审核一级身份，0-全部，1-货方，2-车方
     */

    private Integer regIdentity;
    /**
     * 用户分众类型，参见LabelEnum
     */

    private String userGroup;
    /**
     * 用户分众类型，参见LabelEnum
     */

    private String userGroupName;
    /**
     * 状态，0-停用，1-启用
     */

    private Integer status;

    /**
     * 客户端登录限制 0-无需登录 1 需要登录
     */

    private Integer loginRestriction;


    /**
     * 优先级，数字越大越靠前
     */
    private Integer sort;
    /**
     * 操作人ID
     */
    @JSONField(serialize=false)
    private Long operaUserId;
    /**
     * 操作人名称
     */
    @JSONField(serialize=false)
    private String operaUserName;
    /**
     * 操作人IP地址
     */
    @JSONField(serialize=false)
    private String operaUserIp;
    /**
     * 创建时间
     */
    @JSONField(serialize=false)
    private Date ctime;
    /**
     * 更新时间
     */
    @JSONField(serialize=false)
    private Date mtime;
    /**
     * 是否删除，0-无效，1-有效
     */
    @JSONField(serialize=false)
    private Integer isValid;

    private String linkType;
    private Integer showType;
    private Date startTime;
    private Date endTime;
    private String showProvince;
    private Integer isImportUser;

    /**
     * 0全部 1体验差一手货主
     */
    private Integer userType;

    /**
     * 货主身份 0全部 1个人货主 2企业货主 3货站 4物流公司
     */
    private String goodsIdentity;

    /**
     * 会员状态 0不限 1会员 2非会员
     */
    private Integer vipStatus;

    /**
     * 展示规则 json存储
     */
    private String showRuleContent;

}
