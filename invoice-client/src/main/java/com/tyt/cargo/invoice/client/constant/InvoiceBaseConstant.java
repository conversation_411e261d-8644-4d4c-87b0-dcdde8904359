package com.tyt.cargo.invoice.client.constant;

import java.time.Duration;

/**
 * 全局常量
 */
public class InvoiceBaseConstant {

    public static final String SESSION_HEADER = "ssoInfo";

    //模拟的client sign
    public static final int MOCK_CLIENT_SIGN = 9;
    //模拟的版本
    public static final int MOCK_CLIENT_VERSION = 99999;

    //模拟的终端id
    public static final String MOCK_CLIENT_ID = "invoice-manager";
    public static final Integer MAX_PAGE_SIZE = 30;

    public static class Base{
        /** 全局项目中文名称 **/
        public final static String PROJECT_NAME_ZH = "新后台管理-boot服务";

        public final static String PROJECT_NAME = "invoice-web";

        /** 包名 **/
        public final static String BASE_PACKAGE = "com.tyt.cargo.invoice";

        /** 缓存默认保存时间 **/
        public final static Duration CACHE_TTL = Duration.ofHours(1);

    }

    /** 批量excel 最大条数 **/
    public static final int EXCEL_MAX_COUNT = 100000;

    public static final int OVER_LIMIT_MAX_NUM = 9999;

    /**
     * 未开票订单最大导出数量
     */
    public static final int FIVE_HUNDRED = 500;


    public static final int FIFTY_THOUSAND = 50000;
}
