package com.tyt.cargo.invoice.client.vo.car;

import lombok.Data;

import java.util.Date;
import java.util.List;

/**
 * <AUTHOR>
 * @since 2023/12/07 14:27
 */
@Data
public class AssignCarListBean {

    private Long id;

    private Long userId;

    /**
     * 姓名
     */
    private String name;

    /**
     * 特运通账号
     */
    private String tytCellPhone;

    /**
     * 联系电话
     */
    private String phone;


    /**
     * 市区
     */
    private String city;

    /**
     * 自动市区
     */
    private String autoCity;

    /**
     * 经分市区
     */
    private String biCity;

    /**
     * 签约合作商
     */
    private String signing;

    /**
     * 车型
     */
    private String carType;

    /**
     * 车头信息
     */
    private String headCityNo;

    /**
     * 挂车信息
     */
    private String tailCityNo;

    /**
     * 挂车长度（米）
     */
    private String length;

    /**
     * 货台面高度（厘米）
     */
    private String tableHeight;

    /**
     * 平台样式
     */
    private String otherPureFlat;

    /**
     * 爬梯类型
     */
    private String ladderType;

    /**
     * 备注
     */
    private String remark;

    /**
     * 司机标签：1-兼职运力，2-全职运力
     */
    private Integer driverTag;

    private String carUserFlag;

    private Long infoId;

    private Integer cooperateNum;

    private Integer assignNum;

    private Integer assignSuccessNum;

    private Boolean orders = false;

    private String dispatch;

    private String county;

    private String receivingOrders;

    private String favorableComment;

    private Integer status;

    private Long driverUserId;

    private String drivingAbility;

    private String driverPhone;

    private Integer blackStatus;

    private String distancePreference;

    private List<RouteBean> routeList;

    private String province;

    private Integer sourceType;

    private Integer manualInspection;

    private Date updateTime;

    private Date createTime;

}
