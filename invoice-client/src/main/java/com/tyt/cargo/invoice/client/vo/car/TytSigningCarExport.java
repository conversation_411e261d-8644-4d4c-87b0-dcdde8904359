package com.tyt.cargo.invoice.client.vo.car;

import com.alibaba.excel.annotation.ExcelProperty;
import lombok.Data;
import lombok.EqualsAndHashCode;

/**
 * 专车车辆导出
 *
 * <AUTHOR>
 * @since 2024/07/25
 */
@Data
@EqualsAndHashCode
public class TytSigningCarExport {

    @ExcelProperty(value = {"用户ID"}, index = 1)
    private String userId;

    @ExcelProperty(value = {"姓名"}, index = 2)
    private String name;

    @ExcelProperty(value = {"运距偏好"}, index = 3)
    private String distancePreference;

    @ExcelProperty(value = {"城市"}, index = 4)
    private String city;

    @ExcelProperty(value = {"城市（自动）"}, index = 5)
    private String autoCity;

    @ExcelProperty(value = {"城市（经分）"}, index = 6)
    private String biCity;

    @ExcelProperty(value = {"省份"}, index = 7)
    private String province;

    @ExcelProperty(value = {"路线"}, index = 8)
    private String routeString;

    @ExcelProperty(value = {"签约合作商"}, index = 9)
    private String signing;

    @ExcelProperty(value = {"驾驶能力"}, index = 10)
    private String drivingAbility;

    @ExcelProperty(value = {"是否有线上订单"}, index = 11)
    private String ordersString;

    @ExcelProperty(value = {"是否可接单"}, index = 12)
    private String statusString;

    @ExcelProperty(value = {"接单率(%)"}, index = 13)
    private String receivingOrders;

    @ExcelProperty(value = {"好评率(%)"}, index = 14)
    private String favorableComment;

    @ExcelProperty(value = {"与调度合作次数"}, index = 15)
    private String cooperateNum;

    @ExcelProperty(value = {"指派次数"}, index = 16)
    private String assignNum;

    @ExcelProperty(value = {"指派成功次数"}, index = 17)
    private String assignSuccessNum;

    @ExcelProperty(value = {"车牌"}, index = 18)
    private String carNo;

    @ExcelProperty(value = {"车型"}, index = 19)
    private String carType;

    @ExcelProperty(value = {"挂车长度（米）"}, index = 20)
    private String length;

    @ExcelProperty(value = {"货台面高度（厘米）"}, index = 21)
    private String tableHeight;

    @ExcelProperty(value = {"平台样式"}, index = 22)
    private String otherPureFlat;

    @ExcelProperty(value = {"爬梯类型"}, index = 23)
    private String ladderType;

    @ExcelProperty(value = {"归属调度"}, index = 24)
    private String dispatch;

    @ExcelProperty(value = {"司机标签"}, index = 25)
    private String driverTagString;

    @ExcelProperty(value = {"更新时间"}, index = 26)
    private String updateTime;

    @ExcelProperty(value = {"加入时间"}, index = 27)
    private String createTime;

    @ExcelProperty(value = {"备注"}, index = 28)
    private String remark;

    @ExcelProperty(value = {"仓库"}, index = 29)
    private String county;

    @ExcelProperty(value = {"加入方式"}, index = 30)
    private String sourceTypeString;

    @ExcelProperty(value = {"人工核实状态"}, index = 31)
    private String manualInspectionString;

}
